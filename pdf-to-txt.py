# extract text from hardcoded pdf file name sample.pdf and save it to a text file
import PyPDF2
import sys
import os

pdf

def extract_text_from_pdf(pdf_path, output_path):
    """
    Extract text from a PDF file and save it to a text file.

    Args:
        pdf_path (str): Path to the PDF file
        output_path (str): Path to save the extracted text
    """
    try:
        # Check if PDF file exists
        if not os.path.exists(pdf_path):
            print(f"Error: PDF file '{pdf_path}' not found.")
            return False

        # Open and read the PDF file    
        with open(pdf_path, 'rb') as pdf_file:
            pdf_reader = PyPDF2.PdfReader(pdf_file)

            # Extract text from all pages
            extracted_text = ""
            total_pages = len(pdf_reader.pages)

            print(f"Processing {total_pages} pages...")

            for page_num in range(total_pages):
                page = pdf_reader.pages[page_num]
                page_text = page.extract_text()
                extracted_text += f"--- Page {page_num + 1} ---\n"
                extracted_text += page_text + "\n\n"
                print(f"Processed page {page_num + 1}/{total_pages}")

        # Save extracted text to file
        with open(output_path, 'w', encoding='utf-8') as text_file:
            text_file.write(extracted_text)

        print(f"Text successfully extracted and saved to '{output_path}'")
        print(f"Total characters extracted: {len(extracted_text)}")
        return True

    except Exception as e:
        print(f"An error occurred: {str(e)}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python pdf-to-txt.py <pdf_file_path> <output_text_file_path>")
        sys.exit(1)

    pdf_path = sys.argv[1]
    output_path = sys.argv[2]

    extract_text_from_pdf(pdf_path, output_path)
